from typing import TypedDict, List, Dict, Any, Optional
from typing_extensions import Annotated
import operator
from langgraph.graph.message import add_messages

class WorkflowState(TypedDict):
    """工作流的统一状态定义"""
    # --- 核心输入 ---
    file_path: str
    project_id: int
    
    # --- 文件解析结果 ---
    file_sheets: Dict[str, Any]
    sheet_classifications: Dict[str, str]
    calculation_mode: str
    project_name: str
    sheet_key_columns: Dict[str, List[str]]
    
    # --- 各Agent处理结果 ---
    settlement_data: List[Dict]
    payroll_data: List[Dict]
    
    # --- 最终计算结果 ---
    final_result: Dict[str, Any]
    
    # --- 系统状态 ---
    errors: Annotated[List[str], operator.add]
    current_step: str
    logs: Annotated[List[str], operator.add]

    # --- 交互式Agent所需字段 ---
    # ReAct循环的核心，用于存储对话历史
    messages: Annotated[List, add_messages]
    # 传递给Agent的目标sheet页名称
    sheet_name: Optional[str]
    
    # --- 新增：用于健壮的、状态驱动的路由 ---
    last_tool_name: Optional[str]
    last_tool_status: Optional[str] # 'success' or 'error'