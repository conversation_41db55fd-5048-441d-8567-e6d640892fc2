在这个例子中，我们将创建一个 Agent，它在处理用户请求时，如果发现请求**模糊不清或有歧义**，它不会直接去猜测或调用工具，而是会主动**请求人类澄清**。这是一种更高级的“人机协作”（Human-in-the-Loop），因为中断是由模型自主判断触发的，而不是一个固定的规则。

**场景设定：**
用户输入：“帮我查一下苹果的信息”。
这个请求有歧义：
*   是指苹果公司（Apple Inc.）？
*   还是指苹果这种水果（apple fruit）？

一个优秀的 Agent 应该识别出这种歧义，并向用户提问，而不是盲目地搜索其中一个。

**实现思路：**
1.  **定义一个特殊工具**: 我们将创建一个名为 `request_human_intervention` 的特殊工具。LLM 不会真的“执行”它，而是通过“调用”这个工具来发出一个信号：“我需要帮助！”。
2.  **修改 Agent 的指令**: 我们在 Agent 的系统提示（System Prompt）中明确指示，当遇到模糊不清的请求时，不要猜测，而是调用 `request_human_intervention` 工具来向用户提问。
3.  **构建图 (Graph)**: 图中包含一个条件路由节点。这个节点会检查 Agent 的输出：
    *   如果调用了**普通工具**（如 `search_tool`），则流向工具执行节点。
    *   如果调用了**特殊工具**（`request_human_intervention`），则中断并等待人类输入。
    *   如果没有调用任何工具（直接回答），则结束。
4.  **人机交互**: 当图因 `request_human_intervention` 而中断时，我们的代码会从工具调用中提取出 Agent 想问的问题，展示给用户，并把用户的回答再送回图中，让 Agent 继续处理。

---

### 完整 Python 代码示例

请先确保你已经安装了必要的库：
```bash
pip install langchain langgraph langchain-openai
```
并设置好你的 OpenAI API 密钥。

```python
import os
from typing import TypedDict, Annotated, Sequence
import operator
from langchain_core.messages import BaseMessage, HumanMessage, ToolMessage
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.pregel import GraphRecursionError

# --- 1. 设置环境 ---
# os.environ["OPENAI_API_KEY"] = "sk-..."

# --- 2. 定义工具 (包括特殊工具) ---

@tool
def search_tool(query: str):
    """当需要查询明确的信息时，调用此工具。"""
    print(f"\n--- 正在执行 [search_tool] (查询: {query}) ---")
    if "公司" in query or "Inc" in query:
        return "苹果公司（Apple Inc.）是一家美国的跨国科技公司。"
    elif "水果" in query:
        return "苹果是一种营养丰富的水果，富含维生素。"
    else:
        return f"关于'{query}'的搜索结果：信息不足。"

@tool
def request_human_intervention(question_for_human: str):
    """
    当你遇到模糊不清、有歧义的指令，或者需要人类澄清才能继续执行任务时，调用此工具。
    将你需要问人类的问题作为参数传入。
    """
    # 这个工具本身不执行复杂逻辑，它的“调用”本身就是一个信号。
    print(f"\n--- [request_human_intervention] 被调用，问题: {question_for_human} ---")
    return "已向人类请求澄清，请等待回复。"

# 将所有工具放入列表
tools = [search_tool, request_human_intervention]

# --- 3. 定义 Agent 状态 ---
class AgentState(TypedDict):
    messages: Annotated[Sequence[BaseMessage], operator.add]

# --- 4. 定义图的节点和路由逻辑 ---

# 初始化 LLM 并绑定工具
llm = ChatOpenAI(model="gpt-4o")
# 关键：在系统提示中指导模型何时请求帮助
system_prompt = (
    "你是一个智能助手。你的任务是回答用户的问题，可以借助工具。"
    "如果用户的请求是模糊的、有歧义的，或者你认为在行动前需要人类澄清，"
    "请不要自己猜测，而是必须使用 'request_human_intervention' 工具来向用户提问。"
    "例如，如果用户问'苹果的信息'，你应该问他们是指公司还是水果。"
    "只有当指令明确时，才使用 'search_tool'。"
)
llm_with_tools = llm.bind_tools(tools)

# Agent 节点：调用 LLM
def call_model(state: AgentState):
    print("\n--- 正在调用 LLM 进行决策 ---")
    # 将系统提示和历史消息结合起来
    conversation = [HumanMessage(content=system_prompt)] + state["messages"]
    response = llm_with_tools.invoke(conversation)
    return {"messages": [response]}

# 工具执行节点
def call_tool_node(state: AgentState):
    last_message = state['messages'][-1]
    # 我们只处理 search_tool 的调用
    action = search_tool
    tool_call = last_message.tool_calls[0]
    result = action.invoke(tool_call['args'])
    tool_message = ToolMessage(content=str(result), tool_call_id=tool_call['id'])
    return {"messages": [tool_message]}

# 路由逻辑：决定下一步该怎么走
def router(state: AgentState):
    last_message = state['messages'][-1]
    if not last_message.tool_calls:
        return END # 没有工具调用，直接结束

    # 检查调用的工具名称
    tool_name = last_message.tool_calls[0]['name']
    if tool_name == "request_human_intervention":
        return "human_intervention" # 需要人类介入
    elif tool_name == "search_tool":
        return "action" # 执行普通工具
    else:
        return END

# --- 5. 构建并编译图 ---
workflow = StateGraph(AgentState)

workflow.add_node("agent", call_model)
workflow.add_node("action", call_tool_node)
# 'human_intervention' 节点只是一个路由目标，我们将在它之前中断
workflow.add_node("human_intervention", lambda state: None) 

workflow.set_entry_point("agent")

workflow.add_conditional_edges("agent", router)

workflow.add_edge("action", "agent") # 工具执行完后，返回给 agent 重新思考
# 人类介入后，也要返回给 agent
workflow.add_edge("human_intervention", "agent")

# 关键：在需要人类介入的节点前中断
app = workflow.compile(interrupt_before=["human_intervention"])

# --- 6. 运行 Agent 并处理中断 ---

# 场景1：模糊的请求
print("="*30)
print("场景 1: 运行模糊请求...")
print("="*30)

inputs = {"messages": [HumanMessage(content="帮我查一下苹果的信息")]}

try:
    # 使用 stream 模式运行图，它会在中断点暂停
    for event in app.stream(inputs, stream_mode="values"):
        last_message = event["messages"][-1]
        if last_message.tool_calls and last_message.tool_calls[0]['name'] == 'request_human_intervention':
            print("\n--- 模型请求人工干预 ---")
            # 从工具调用中提取模型想问的问题
            question = last_message.tool_calls[0]['args']['question_for_human']
            print(f"模型提问: {question}")
            
            # 获取人类的输入
            human_response = input("你的回答: ")
            
            # 将人类的回答包装成 ToolMessage，让 Agent 继续处理
            # 这里的 tool_call_id 必须匹配，让 Agent 知道这个回复是针对哪个请求的
            clarification_message = ToolMessage(
                content=human_response,
                tool_call_id=last_message.tool_calls[0]['id']
            )
            
            # 从中断处继续图的执行
            print("\n--- 将人类的澄清信息送回模型 ---")
            inputs = {"messages": [clarification_message]}
        
        elif last_message.content:
             print(f"\n最终答案: {last_message.content}")

except (GraphRecursionError, KeyboardInterrupt):
    print("\n--- 流程被中断 ---")

print("\n\n" + "="*30)
print("场景 2: 运行明确请求...")
print("="*30)

# 场景2：明确的请求，不会触发中断
inputs = {"messages": [HumanMessage(content="我想了解一下苹果公司这家企业")]}
for event in app.stream(inputs, stream_mode="values"):
    last_message = event["messages"][-1]
    if last_message.content:
        print(f"\n最终答案: {last_message.content}")

print("\n--- 流程结束 ---")
```

### 如何运行和交互

1.  将代码保存为 `anomaly_agent.py` 并运行 `python anomaly_agent.py`。

2.  **场景1 (模糊请求) 的输出与交互**:
    *   程序会首先运行，LLM 接收到 "帮我查一下苹果的信息"。
    *   根据我们的指令，它会判断出这是一个模糊请求，并决定调用 `request_human_intervention` 工具。
    *   此时，图的执行会中断，并向你提问。

    ```text
    ==============================
    场景 1: 运行模糊请求...
    ==============================

    --- 正在调用 LLM 进行决策 ---

    --- [request_human_intervention] 被调用，问题: 请问您是指苹果公司（Apple Inc.）还是苹果这种水果？ ---

    --- 模型请求人工干预 ---
    模型提问: 请问您是指苹果公司（Apple Inc.）还是苹果这种水果？
    你的回答: 
    ```
    *   你在终端输入 `我指的是公司` 并按回车。
    *   程序会将你的回答送回给 Agent，Agent 收到澄清后，会再次决策，这次它会调用 `search_tool`。
    *   最终，它会根据搜索结果给出答案。

    ```text
    你的回答: 我指的是公司

    --- 将人类的澄清信息送回模型 ---

    --- 正在调用 LLM 进行决策 ---

    --- 正在执行 [search_tool] (查询: 苹果公司信息) ---

    --- 正在调用 LLM 进行决策 ---

    最终答案: 苹果公司（Apple Inc.）是一家美国的跨国科技公司。
    ```

3.  **场景2 (明确请求) 的输出**:
    *   在这个场景中，请求是明确的，所以 Agent 不会请求帮助，而是直接调用搜索工具并给出答案，整个流程不会中断。

    ```text
    ==============================
    场景 2: 运行明确请求...
    ==============================

    --- 正在调用 LLM 进行决策 ---

    --- 正在执行 [search_tool] (查询: 苹果公司) ---

    --- 正在调用 LLM 进行决策 ---

    最终答案: 苹果公司（Apple Inc.）是一家美国的跨国科技公司。

    --- 流程结束 ---
    ```

这个例子充分展示了如何通过“引导模型调用特定工具”的方式，来创建一个能自主判断何时需要人类帮助的、更加智能和鲁棒的 Agent。