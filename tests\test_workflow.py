import unittest
import os
import tempfile
import pandas as pd
from dotenv import load_dotenv
from src.workflow import HesuanWorkflow
from src.state import WorkflowState

# 加载 .env 文件中的环境变量
load_dotenv()

class TestHesuanWorkflow(unittest.TestCase):
    """测试保理核额审查工作流"""
    
    def setUp(self):
        """测试前准备"""
        self.workflow = HesuanWorkflow()
        self.test_data_path = "testdata/测试V2.xlsx"
    
    def tearDown(self):
        """在每个测试后清理"""
        # 显式关闭数据库连接
        if hasattr(self, 'workflow') and hasattr(self.workflow, 'db'):
            self.workflow.db.close()

    def test_file_parser_agent(self):
        """测试文件解析Agent"""
        if not os.path.exists(self.test_data_path):
            self.skipTest("测试文件不存在")
        
        # 测试文件解析
        result = self.workflow.file_parser.parse_excel_file(self.test_data_path)
        
        # 验证解析结果
        self.assertIsInstance(result, dict)
        self.assertGreater(len(result), 0)
        
        # 验证每个sheet都有必要的信息
        for sheet_name, sheet_info in result.items():
            self.assertIn('columns', sheet_info)
            self.assertIn('shape', sheet_info)
            self.assertIn('sample_data', sheet_info)
    
    def test_workflow_integration(self):
        """测试完整工作流集成"""
        if not os.path.exists(self.test_data_path):
            self.skipTest("测试文件不存在")
        
        # 运行完整工作流，并启用流式日志
        result = self.workflow.run(self.test_data_path, stream_log=True)
        
        # 验证结果结构
        self.assertIsInstance(result, dict)
        
        # 检查是否有错误
        if result.get("errors"):
            logs = "\n".join(result.get("logs", []))
            self.fail(f"工作流执行出错: {result['errors']}\n\nLogs:\n{logs}")
        
        # 验证最终结果
        final_result = result.get("final_result", {})
        self.assertIn("formatted_result", final_result)
        self.assertIn("detailed_report", final_result)
        
        # 验证核心数据
        formatted_result = final_result.get("formatted_result", {})
        self.assertIn("项目名称", formatted_result)
        self.assertIn("最终核额金额", formatted_result)
        self.assertIn("核额模式", formatted_result)
    
    def test_expected_output_format(self):
        """测试期望的输出格式"""
        if not os.path.exists(self.test_data_path):
            self.skipTest("测试文件不存在")
        
        result = self.workflow.run(self.test_data_path, stream_log=True)
        
        if result.get("errors"):
            logs = "\n".join(result.get("logs", []))
            self.fail(f"工作流执行出错: {result['errors']}\n\nLogs:\n{logs}")
        
        formatted_result = result.get("final_result", {}).get("formatted_result", {})
        
        # 验证输出格式符合O3的预期
        expected_keys = [
            "项目名称", "核额模式", "结算总额", "折算比例", 
            "折后金额", "发薪金额", "最终核额金额", "汇总金额是否冲突"
        ]
        
        for key in expected_keys:
            self.assertIn(key, formatted_result, f"缺少必要字段: {key}")
        
        # 验证数据类型
        self.assertIsInstance(formatted_result["结算总额"], (int, float))
        self.assertIsInstance(formatted_result["折算比例"], (int, float))
        self.assertIsInstance(formatted_result["最终核额金额"], (int, float))
        self.assertIsInstance(formatted_result["汇总金额是否冲突"], bool)

class TestAgentComponents(unittest.TestCase):
    """测试各个Agent组件"""
    
    def test_database_operations(self):
        """测试数据库操作"""
        from src.database import DatabaseManager
        
        # 使用临时数据库
        # 使用临时数据库
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            db_path = tmp.name
            # 确保文件句柄已关闭
            tmp.close()
        
        try:
            db = DatabaseManager(db_path)
            
            # 测试创建项目
            project_id = db.create_project("测试项目", "test.xlsx")
            self.assertIsInstance(project_id, int)
            self.assertGreater(project_id, 0)
            
            # 测试保存员工信息
            employees = [
                {"employee_id": "001", "employee_name": "张三"},
                {"employee_id": "002", "employee_name": "李四"}
            ]
            db.save_employees(project_id, employees)
            
            # 测试获取员工信息
            retrieved_employees = db.get_employees(project_id)
            self.assertEqual(len(retrieved_employees), 2)
            
            # 显式关闭连接，释放文件锁
            db.close()

        finally:
            # 清理临时文件
            if os.path.exists(db_path):
                try:
                    os.unlink(db_path)
                except PermissionError:
                    print(f"无法删除临时数据库 {db_path}，可能被占用。")

if __name__ == '__main__':
    unittest.main()