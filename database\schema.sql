-- 保理核额审查系统数据库表结构

-- 项目表
CREATE TABLE IF NOT EXISTS projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_name TEXT NOT NULL,
    file_path TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 员工基础信息表（用于人员匹配）
CREATE TABLE IF NOT EXISTS employees (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER,
    employee_id TEXT,
    employee_name TEXT,
    FOREIGN KEY (project_id) REFERENCES projects(id)
);

-- 结算金额表（对账单/用工统计表核心数据）
CREATE TABLE IF NOT EXISTS settlement_amounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER,
    employee_id TEXT,
    employee_name TEXT,
    settlement_amount REAL,      -- 最终结算金额 (计算后填充)
    source_type TEXT,            -- 'account_bill' 或 'work_statistics'
    
    -- 新增的预处理列 (可为空), 用于暂存 work_statistics 的原始数据
    preprocessed_quantity REAL,  -- 预处理的数量 (工时/班次)
    preprocessed_price REAL,     -- 预处理的单价 (时薪/班次单价)
    preprocessed_amount REAL,    -- 预处理的直接金额

    FOREIGN KEY (project_id) REFERENCES projects(id)
);

-- 发薪金额表（发薪流水/应发薪表核心数据）
CREATE TABLE IF NOT EXISTS payroll_amounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER,
    employee_id TEXT,
    employee_name TEXT,
    payroll_amount REAL,     -- 实发/应发金额
    source_type TEXT,        -- 'actual_pay' 或 'expected_pay'
    FOREIGN KEY (project_id) REFERENCES projects(id)
);

-- 最终计算结果表
CREATE TABLE IF NOT EXISTS calculation_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER,
    project_name TEXT,
    calculation_mode TEXT,   -- 核额模式
    settlement_total REAL,   -- 结算总额
    discount_rate REAL,      -- 折算比例
    discounted_amount REAL,  -- 折后金额
    payroll_total REAL,      -- 发薪总额
    final_credit_amount REAL, -- 最终核额
    summary_conflict BOOLEAN, -- 汇总冲突
    matched_employees INTEGER, -- 匹配员工数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id)
);