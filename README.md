# 保理核额审查多Agent系统

基于LangGraph构建的智能保理核额审查系统，支持多种Excel表格格式的自动化处理。

## 功能特性

- **智能文件解析**: 自动识别Excel文件中的不同Sheet类型
- **多Agent协作**: 5个专业Agent协同处理不同数据类型
- **灵活适配**: 支持不同表格结构和字段命名
- **精确计算**: 严格按照保理业务规则进行核额计算
- **详细报告**: 生成完整的处理报告和结果输出

## 系统架构

### 核心Agent
1. **文件解析分类Agent** - 识别Excel文件类型和sheet分类
2. **对账表处理Agent** - 处理对账单数据提取和计算
3. **用工统计表处理Agent** - 处理用工统计数据
4. **发薪流水处理Agent** - 处理发薪流水数据和人员匹配
5. **核算输出Agent** - 执行最终计算和结果输出

### 工作流图

```mermaid
graph TD
    A[Start] --> B(FileParserAgent);
    B -- 识别文件类型和模式 --> C{路由};
    C -- 账单模式 --> D[AccountBillProcessor];
    C -- 非账单模式 --> E[WorkStatisticsProcessor];
    D -- 结算数据 --> F[PayrollProcessor];
    E -- 结算数据 --> F;
    F -- 匹配后的发薪数据 --> G[CalculationAgent];
    G -- 最终核算结果 --> H[End];

    subgraph "数据交互"
        D -- "保存结算数据到DB" --> DB[(SQLite)];
        E -- "保存结算数据到DB" --> DB;
        F -- "保存发薪数据到DB" --> DB;
        G -- "从DB读取数据" --> DB;
    end

    style B fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#bbf,stroke:#333,stroke-width:2px
    style F fill:#bbf,stroke:#333,stroke-width:2px
    style G fill:#f9f,stroke:#333,stroke-width:2px
```

### 计算模式
- **账单模式-先发后融**: 对账单 + 发薪流水 (折算比例80%)
- **账单模式-先融后发**: 对账单 + 应发薪表 (折算比例80%)
- **非账单模式-先发后融**: 用工统计表 + 发薪流水 (折算比例70%)
- **非账单模式-先融后发**: 用工统计表 + 应发薪表 (折算比例70%)

### Agent详细设计

本系统的核心优势在于其处理不规范Excel文件的鲁棒性。这主要通过在数据处理Agent内部实现的**智能数据清洗与处理**机制来完成。

1.  **`文件解析分类Agent` (`FileParserAgent`)**
    -   **角色**: 系统的数据入口和高级调度员。
    -   **流程**:
        -   **文件解析**: 读取Excel文件，获取所有Sheet的名称和数据样本。
        -   **LLM分类**: 将Sheet的结构和样本数据发送给LLM，识别每个Sheet的类型（对账单、发薪流水等）和整个文件的计算模式（例如：“账单模式-先发后融”）。
        -   **状态传递**: 将原始数据、分类结果和计算模式传递给下游的专业处理Agent。
        -   **数据库交互**: 在数据库中创建项目记录，并返回项目ID。

2.  **`对账表处理Agent` (`AccountBillProcessor`)**
    -   **角色**: 负责对账单的端到端清洗、计算和持久化。
    -   **智能清洗流程**:
        -   **LLM语义映射**: Agent以无表头模式加载Sheet的原始数据样本，发送给LLM，识别出关键业务字段（如“员工姓名”、“结算金额”）的**实际列名**。
        -   **代码精确定位**: Agent的Python代码接收到LLM返回的真实列名，逐行扫描原始数据，**精确定位**包含所有这些列名的表头行号。
        -   **可靠加载**: 使用定位到的行号作为`header`参数，重新加载整个Sheet，得到一个列名正确、可直接用于计算的DataFrame。
    -   **数据计算与持久化**:
        -   **汇总行处理**: 根据LLM识别的汇总行标识（如“合计”），或通过空值判断，移除所有汇总行。
        -   **金额计算**: 逐行读取金额列，进行求和，得到**不受原始表格“合计”行影响**的精确结算总额。
        -   **数据库存储**: 将清洗和计算后的每条结算记录（员工、金额等）保存到SQLite数据库中，供后续Agent使用。

3.  **`用工统计表处理Agent` (`WorkStatisticsProcessor`)**
    -   **角色**: 负责用工统计表的端到端清洗、计算和持久化。
    -   **核心设计**: 其工作流程与`对账表处理Agent`高度相似，同样采用**LLM语义映射** + **代码精确定位**的两步式清洗流程。
    -   **关键差异**:
        -   LLM识别的关键列不同，主要关注“工时”、“单价”或“应发金额”等字段。
        -   计算逻辑根据识别到的列进行调整（例如，工时 × 单价）。
    -   **数据计算与持久化**: 与对账表Agent类似，计算出总金额后，将结构化数据存入数据库。

4.  **`发薪流水处理Agent` (`PayrollProcessor`)**
    -   **角色**: 负责发薪流水或应发薪表的清洗、匹配和持久化。
    -   **智能清洗流程**: 同样采用与`对账表处理Agent`一致的两步式清洗流程，识别“姓名”、“实发金额”等关键列。
    -   **数据计算与持久化**:
        -   **人员匹配**: 从数据库中读取上一步（对账单/用工表）存入的员工列表，与当前发薪表中的员工进行匹配。
        -   **金额计算**: **仅对匹配成功的员工**，将其发薪金额进行累加，得到有效的发薪总额。
        -   **数据库存储**: 将匹配后的发薪记录保存到数据库。

5.  **`核算输出Agent` (`CalculationAgent`)**
    -   **角色**: 最终的计算和报告生成器。
    -   **流程**:
        -   **数据汇总**: 从工作流状态中接收由上游Agent计算出的结算总额和发薪总额。
        -   **规则计算**: 严格按照预设的业务规则（例如，折算比例、取两者小值）执行最终的核额计算。
        -   **报告生成**: 将所有结果格式化，生成最终的表格、JSON或详细文本报告。

## 安装和使用

### 环境要求
- Python 3.11+
- uv包管理器

### 安装依赖
```bash
uv sync
```

### 基本使用
```bash
# 处理Excel文件
python main.py process data.xlsx

# 详细输出模式
python main.py process data.xlsx --verbose

# 输出JSON格式
python main.py process data.xlsx --format json

# 生成详细报告
python main.py process data.xlsx --format report --save

# 演示模式
python main.py demo
```

### 输出格式
- **table**: 表格形式显示结果
- **json**: JSON格式输出
- **report**: 详细报告格式

## 项目结构

```
project-hesuan/
├── src/
│   ├── agents/           # Agent实现
│   │   ├── file_parser.py
│   │   ├── account_bill.py
│   │   ├── work_statistics.py
│   │   ├── payroll.py
│   │   └── calculation.py
│   ├── workflow.py       # 工作流编排
│   ├── state.py         # 状态定义
│   ├── database.py      # 数据库管理
│   └── cli.py           # 命令行界面
├── database/
│   └── schema.sql       # 数据库结构
├── testdata/           # 测试数据
├── tests/              # 测试用例
└── main.py             # 主入口
```

## 技术栈

- **LangGraph**: 多Agent工作流编排
- **LangChain**: LLM集成和调用
- **pandas**: Excel数据处理
- **SQLite**: 本地数据存储
- **Typer**: 命令行界面
- **Rich**: 美化终端输出

## 开发说明

### 运行测试
```bash
uv run pytest tests/
```

### 代码格式化
```bash
uv run black src/
uv run ruff src/
```

## 许可证

本项目采用MIT许可证。