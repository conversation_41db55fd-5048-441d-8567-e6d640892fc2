import os
from dotenv import load_dotenv
from langchain.chat_models import ChatOpenAI

# 加载 .env 文件
load_dotenv()

def test_openrouter_connectivity():
    """
    一个独立的脚本，用于测试与OpenRouter API的连接。
    """
    api_key = os.getenv("OPENAI_API_KEY")
    model_name = os.getenv("MODEL_NAME")
    base_url = os.getenv("OPENAI_BASE_URL")

    print("--- 开始测试OpenRouter连通性 ---")
    print(f"模型: {model_name}")
    print(f"Base URL: {base_url}")
    print(f"API Key: {'*' * (len(api_key) - 4) + api_key[-4:] if api_key else 'None'}")
    
    if not all([api_key, model_name, base_url]):
        print("\n错误：必要的环境变量 (OPENROUTER_API_KEY, MODEL_NAME, OPENROUTER_BASE_URL) 未设置。")
        return

    try:
        # 初始化 ChatOpenAI
        llm = ChatOpenAI(
            model_name=model_name,
            openai_api_key=api_key,
            openai_api_base=base_url,
            temperature=0,
            # model_kwargs={
            #     "headers": {
            #         "HTTP-Referer": os.getenv("YOUR_SITE_URL", "http://localhost:3000"),
            #         "X-Title": os.getenv("YOUR_SITE_NAME", "My Awesome App"),
            #     }
            # },
        )

        # 发送一个简单的请求
        print("\n正在发送请求...")
        response = llm.invoke("Hello, world!")
        
        print("\n请求成功！")
        print("返回内容:")
        print(response.content)

    except Exception as e:
        print(f"\n请求失败: {e}")
    finally:
        print("\n--- 测试结束 ---")

if __name__ == "__main__":
    test_openrouter_connectivity()