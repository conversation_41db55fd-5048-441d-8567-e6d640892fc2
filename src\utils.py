import pandas as pd
from typing import Optional, List, Dict, Any
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text

# ------------------------------------------------------------------------------
# Console <PERSON>
# ------------------------------------------------------------------------------

class ConsoleLogger:
    """A centralized logger for rich terminal outputs."""
    
    def __init__(self):
        self.console = Console()

    def step(self, message: str):
        """Prints a step message."""
        self.console.print(f"\n[bold cyan]下一步: {message}[/bold cyan]")

    def agent_start(self, agent_name: str):
        """Prints a panel indicating the start of an agent."""
        self.console.print(Panel(f"[bold]开始执行: {agent_name}[/bold]", style="blue", expand=False))

    def agent_end(self, agent_name: str):
        """Prints a message indicating the end of an agent."""
        self.console.print(f"[blue]✅ {agent_name} 执行完毕.[/blue]")

    def info(self, message: str):
        """Prints an informational message."""
        self.console.print(f"[dim]  - {message}[/dim]")

    def success(self, message: str):
        """Prints a success message."""
        self.console.print(f"[green]  ✓ {message}[/green]")

    def error(self, message: str):
        """Prints an error message."""
        self.console.print(f"[bold red]  ✗ 错误: {message}[/bold red]")

    def table(self, title: str, columns: List[str], rows: List[List[Any]]):
        """Prints a table."""
        table = Table(title=title, show_header=True, header_style="bold magenta")
        for col in columns:
            table.add_column(col)
        for row in rows:
            table.add_row(*[str(item) for item in row])
        self.console.print(table)

    def panel(self, content: str, title: str, style: str = "green"):
        """Prints a panel with content."""
        self.console.print(Panel(content, title=title, border_style=style, expand=False))
        
    def llm_result(self, title: str, result: Dict[str, Any], reasoning: str = ""):
        """Formats and prints the result from an LLM call."""
        
        content = ""
        for key, value in result.items():
            # Format list values nicely
            if isinstance(value, list):
                value_str = "\n".join([f"    - {item}" for item in value])
                content += f"[bold]{key}:[/bold]\n{value_str}\n"
            else:
                content += f"[bold]{key}:[/bold] {value}\n"
        
        if reasoning:
            content += f"\n[bold]决策依据:[/bold]\n{reasoning}"

        self.console.print(Panel(Text(content.strip()), title=f"[bold yellow]{title}[/bold yellow]", border_style="yellow"))


# Instantiate the logger for global use
logger = ConsoleLogger()


# ------------------------------------------------------------------------------
# Original Excel Utility
# ------------------------------------------------------------------------------

def get_excel_sample(file_path: str, sheet_name: str, sample_rows: int = 10) -> Optional[str]:
    """
    智能地从Excel的特定sheet中读取数据样本。

    该函数会:
    1. 读取整个sheet。
    2. 找到第一个非完全空的行。
    3. 从该行开始，提取指定数量的行作为样本。
    4. 将样本转换为字符串格式，用于LLM的prompt。

    Args:
        file_path: Excel文件路径。
        sheet_name: 需要读取的sheet名称。
        sample_rows: 需要提取的样本行数。

    Returns:
        如果成功，返回包含样本数据的字符串；如果sheet为空或无法处理，返回None。
    """
    try:
        # 以无表头模式加载整个sheet
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)

        # 如果DataFrame为空，直接返回
        if df.empty:
            return None

        # 找到第一个非完全空的行的索引
        first_valid_row = -1
        for i, row in df.iterrows():
            if not row.isnull().all():
                first_valid_row = i
                break
        
        # 如果没有找到有效行（整个sheet都是空的）
        if first_valid_row == -1:
            return None

        # 从第一个有效行开始，提取样本
        df_sample = df.iloc[first_valid_row : first_valid_row + sample_rows]
        
        # 将样本转换为字符串
        sample_str = df_sample.to_string(index=False, header=False)
        
        return sample_str

    except Exception as e:
        logger.error(f"读取Excel样本时出错 (Sheet: {sheet_name}): {e}")
        return None