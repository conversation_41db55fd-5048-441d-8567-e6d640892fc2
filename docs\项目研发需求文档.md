目前我已经尝试使用 [[项目需求提示词]] ，利用gpt o3的自主规划智能体，实现了我对excel数据的处理，具体的O3的规划和执行过程输出见[[gpt-o3 agent的输出]]。
因为O3的agent是一个通用的agent，而O3是一个昂贵的模型，现在我希望不是使用通用的agent+昂贵的高性能模型去完成这个事情，我希望构建一个agentic workflow，这是一个多智能体系统。我希望将整个流程分解成不同的子步骤，每个子步骤由sub agent去完成，这样能够降低对单一模型能力的要求和依赖，使得工作流的执行更加稳定可靠。
比如，按照如下拆分：
1. 文件解析分类和模式判断Agent
2. 对账表处理agent
3. 用工统计表处理agent
4. 发薪流水/应发薪表处理agent
5. 核算和输出Agent：需要生成代码进行计算

注意事项：
1. 请注意，里面大部分的agent基本都需要具备code执行并获取输出的能力，而且需要机制确保生成的code能够正确执行：
	1. 这里面涉及到对excel文件的处理，这里需要用到python代码，执行代码的输出并获取输出内容；
	2. 里面涉及到精确的数值计算的部分，也需要使用代码进行计算；

2. 建议整个agent先以终端的形式呈现，输入时拖入的excel文件路径，直接在终端输出最终需要的信息；
3. 关于数据的存储和交互，比如多个agent都要用到对账表的数据，比如其中最复杂的发薪流水汇总计算的时候要在对账表中找到对应的性能。这里我建议引入简单的本地数据库系统存储各个表格的数据，进行本地持久化存储。避免引入复杂的数据库，因为只涉及到2-3个表格，选用最简单的数据库

技术栈选型：希望选择成熟的agent框架，就是用langgraph这个框架进行。使用context 7 mcp获取最新的文档支持完成工作。

