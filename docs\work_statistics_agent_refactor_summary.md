# "用工统计"智能体 (Work Statistics Agent) 最终架构方案

## 1. 核心思想："模式驱动"

本 Agent 的最终架构遵循"模式驱动"的设计思想，旨在实现高效、健壮和职责清晰的目标。

- **Agent 职责收敛**: Agent 的核心职责被精确定义为 **"分析与决策"**。它通过分析工具获取信息，最终决策调用哪个业务模式的执行工具。
- **工具职责整合**: 我们提供与核心业务模式一一对应的**原子化执行工具**。每个工具在内部封装了"定位数据 -> 计算 -> 存储"的完整、确定性流程。
- **数据流优化**: 所有大规模的表格数据均在代码层内部流动和处理，不通过LLM的上下文传递，从根本上解决了性能瓶颈和Token成本问题。
- **状态连续性**: 通过决策路由机制，确保人机交互后工作流能够从正确的节点继续执行，避免重新开始整个流程。

## 2. 最终工具集

### 2.1. 分析工具
- `get_sheet_preview(...)`: 用于分析表格结构，是 Agent 的第一个动作。
- `ask_user_for_clarification(...)`: 用于在信息不足时向用户提问，以补全决策所需信息。

### 2.2. 执行工具 (原子操作)
Agent 在分析结束后，根据判断选择**一个**执行工具来完成所有后续工作：
- `process_with_multiplication(...)`: 处理所有"数量 x 单价"的场景（如 工时*时薪，班次*班次单价）。
- `process_with_fixed_rate(...)`: 处理"数量 x 固定单价"的场景。
- `process_from_direct_amount(...)`: 处理直接从表格中提取最终金额的场景。

## 3. 工作流程示例 (统一单价场景)

1.  **Agent (分析)**: 调用 `get_sheet_preview`，发现只有"姓名"和"工时"列，缺少单价信息。
2.  **Agent (分析)**: 调用 `ask_user_for_clarification`，向用户提问并获得 `fixed_rate=25.0` 的答复。
3.  **Agent (决策与执行)**: 此时信息充分，Agent 做出最终决策，调用**唯一一次**执行工具：
    ```python
    process_with_fixed_rate(
        project_id=..., 
        file_path=..., 
        sheet_name=..., 
        column_map={'employee_name': '姓名', 'quantity': '工时'}, 
        fixed_rate=25.0
    )
    ```
4.  **工具**: `process_with_fixed_rate` 函数在内部完成所有工作（加载、计算、存储），并将成功摘要返回给Agent。
5.  **完成**: Agent 收到成功摘要，任务结束。

## 4. 关键技术实现

### 4.1. 状态连续性保障
- **智能路由**: `decide_next_step` 函数能够正确识别消息类型，确保用户澄清后流程从正确节点继续。
- **消息类型检查**: 对 `AIMessage` 和 `ToolMessage` 的处理逻辑，避免工作流重新开始。
- **中断恢复**: 通过 LangGraph 的 `interrupt_before` 机制和检查点保存，实现可靠的人机交互流程。

### 4.2. 决策路由实现
```python
def decide_next_step(state: WorkflowState) -> str:
    """决策函数，确保状态连续性"""
    last_message = state['messages'][-1]
    
    # 检查消息类型，确保正确的流程控制
    if not isinstance(last_message, AIMessage):
        return "call_tool_and_continue"  # 回到Agent继续思考
    
    if not last_message.tool_calls:
        return END
    
    tool_name = last_message.tool_calls[0]['name']
    if tool_name == 'ask_user_for_clarification':
        return "ask_user"
    elif tool_name == 'get_sheet_preview':
        return "call_tool_and_continue"
    else:
        return "call_tool_and_end"
```

## 5. 架构优势

- **高效**: 数据在代码层流动，避免了高昂的Token成本和性能瓶颈。
- **简洁**: LLM的决策链被缩短为"分析 -> 单次执行"，降低了出错概率和资源消耗。
- **职责清晰**: Agent 专注于其最擅长的"分析与决策"，而将确定性的执行流程完全交给封装好的代码工具，实现了完美的"人机"协作。
- **状态可靠**: 通过路由机制和状态管理，确保人机交互不会破坏工作流的连续性。

## 6. 与 LangGraph 工作流的集成

### 6.1. 节点设计
- **work_statistics_agent**: 负责思考和决策的Agent节点
- **work_statistics_tools**: 负责执行具体工具的工具节点  
- **ask_user**: 专门处理人机交互的中断节点

### 6.2. 边和路由
```python
# Agent思考后的决策路由
workflow.add_conditional_edges(
    "work_statistics_agent",
    decide_next_step,
    {
        "call_tool_and_continue": "work_statistics_tools",
        "call_tool_and_end": "work_statistics_tools", 
        "ask_user": "ask_user",
        END: END
    }
)

# 人机交互后返回Agent继续思考
workflow.add_edge("ask_user", "work_statistics_agent")
```

这种设计确保了工作流的状态连续性和执行的可靠性。
