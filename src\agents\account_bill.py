import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import os
import os
import re
import json
from langchain_community.chat_models import MoonshotChat
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel, Field

from ..state import WorkflowState
from ..database import DatabaseManager
from ..utils import get_excel_sample, logger

class ColumnMapping(BaseModel):
    """列映射结果"""
    employee_id_column: Optional[str] = Field(description="员工ID列名")
    employee_name_column: str = Field(description="员工姓名列名")
    settlement_amount_column: str = Field(description="结算金额列名")
    summary_row_indicators: List[str] = Field(description="汇总行标识符", default=[])

class AccountBillProcessor:
    """对账表处理Agent"""
    
    def __init__(self):
        model_name = os.getenv("MODEL_NAME", "moonshot-v1-8k")
        temperature = float(os.getenv("TEMPERATURE", 0.3))
        self.llm = MoonshotChat(model=model_name, temperature=temperature)
        self.db = DatabaseManager()
    
    def find_account_bill_sheet(self, state: WorkflowState) -> Optional[str]:
        """找到对账单sheet"""
        for sheet_name, classification in state["sheet_classifications"].items():
            if classification == "对账单":
                return sheet_name
        return None
    
    def analyze_column_mapping(self, file_path: str, sheet_name: str) -> ColumnMapping:
        """使用LLM分析原始数据，找出关键列的实际名称"""
        
        sample_data_str = get_excel_sample(file_path, sheet_name, sample_rows=20)
        if not sample_data_str:
            raise Exception(f"无法从sheet '{sheet_name}' 中读取到有效的数据样本。")
        
        system_prompt = f"""你是一个顶级的Excel数据分析师。你的任务是分析给定的表格原始数据样本，告诉我用于识别员工和金额的关键列的 **实际名称** 是什么。

        **任务**:
        请仔细分析下面的原始数据，并严格按照这个JSON格式返回你的分析结果:
        ```json
        {{
          "employee_id_column": "代表员工ID的列的实际名称 (如果存在)",
          "employee_name_column": "代表员工姓名的列的实际名称",
          "settlement_amount_column": "代表最终结算金额的列的实际名称",
          "summary_row_indicators": ["用于识别汇总行的文本列表，例如 '合计'"]
        }}
        ```

        **关键列识别指南**:
        1. `employee_name_column`: 找到最能代表 **员工姓名** 的那一列的名称。
        2. `settlement_amount_column`: 找到最能代表 **最终结算总金额** 的列的名称。这通常是含税的、最后的金额，比如 "开票金额", "含税金额", "结算金额"。
        3. `employee_id_column`: 找到代表 **员工工号或编号** 的列的名称。如果找不到，可以返回 `null`。
        4. `summary_row_indicators`: 返回一个字符串列表，包含用于识别表格中汇总行的文本。例如，如果姓名列包含 "合计" 或 "总计" 的行是汇总行，则此处应为 `["合计", "总计"]`。

        你返回的必须是数据中真实存在的列名。"""

        human_prompt = f"""这是表格的前20行原始数据:
        
        {sample_data_str}
        
        请返回包含上述关键列实际名称的JSON对象。"""

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=human_prompt)
        ]
        
        # 移除旧的打印逻辑

        # 调用LLM
        response = self.llm.invoke(messages)
        response_content = response.content

        try:
            # 尝试从响应中提取JSON
            match = re.search(r"```json\n(.*)\n```", response_content, re.DOTALL)
            if match:
                json_str = match.group(1)
            else:
                json_str = response_content
            
            # 显式地从字典创建Pydantic模型
            parsed_dict = json.loads(json_str)
            return ColumnMapping(**parsed_dict)
        except (json.JSONDecodeError, TypeError, ValueError) as e:
            # 如果LLM返回的不是有效的JSON，或者Pydantic验证失败，则直接抛出异常
            raise Exception(f"LLM返回的列映射结果无效或格式错误: {e}\n原始返回内容:\n{response_content}")

    def find_header_row(self, df_raw: pd.DataFrame, column_mapping: ColumnMapping) -> int:
        """
        在原始DataFrame中，通过代码定位包含所有关键列名的表头行。
        """
        required_cols = [
            column_mapping.employee_name_column,
            column_mapping.settlement_amount_column
        ]
        # 如果有ID列，也加入必须查找的列表
        if column_mapping.employee_id_column:
            required_cols.append(column_mapping.employee_id_column)

        for i, row in df_raw.head(10).iterrows():
            row_values = set(str(v).strip() for v in row.values)
            if all(col in row_values for col in required_cols):
                return i
        
        raise Exception(f"在表格中未能定位到包含所有关键列 '{', '.join(required_cols)}' 的表头行。")
    
    def remove_summary_rows(self, df: pd.DataFrame, column_mapping: ColumnMapping) -> pd.DataFrame:
        """移除汇总行"""
        try:
            # 创建一个初始全为True的掩码，索引与df对齐
            mask = pd.Series(True, index=df.index)

            # 方法1: 根据员工姓名列识别
            name_col = column_mapping.employee_name_column
            if name_col in df.columns:
                for indicator in column_mapping.summary_row_indicators:
                    # 使用 .loc 确保索引对齐
                    mask.loc[df[name_col].astype(str).str.contains(indicator, na=False)] = False

            # 方法2: 根据员工ID列识别（如果存在）
            id_col = column_mapping.employee_id_column
            if id_col and id_col in df.columns:
                mask.loc[df[id_col].isna()] = False

            # 方法3: 根据序号列识别（如果存在）
            seq_columns = [col for col in df.columns if '序号' in col]
            if seq_columns:
                seq_col = seq_columns[0]
                mask.loc[df[seq_col].isna()] = False
            
            # 使用 .loc 应用掩码，这是最安全的方式
            return df.loc[mask].copy()

        except Exception as e:
            print(f"移除汇总行时出错: {str(e)}")
            return df
    
    def calculate_settlement_amounts(self, df: pd.DataFrame, column_mapping: ColumnMapping) -> Tuple[List[Dict], float, bool]:
        """计算结算金额"""
        try:
            # 获取关键列
            name_col = column_mapping.employee_name_column
            amount_col = column_mapping.settlement_amount_column
            id_col = column_mapping.employee_id_column
            
            # 检查必要列是否存在
            if name_col not in df.columns or amount_col not in df.columns:
                raise Exception(f"必要列不存在: {name_col}, {amount_col}")
            
            # 移除汇总行
            clean_df = self.remove_summary_rows(df, column_mapping)
            
            # 计算总金额（在移除汇总行之前保存原始汇总用于验证）
            original_total = None
            try:
                # 尝试从原始数据中找到汇总行
                for indicator in column_mapping.summary_row_indicators:
                    summary_rows = df[df[name_col].astype(str).str.contains(indicator, na=False, case=False)]
                    if not summary_rows.empty and amount_col in summary_rows.columns:
                        original_total = summary_rows[amount_col].iloc[0]
                        break
            except:
                pass
            
            # 从清理后的数据计算
            settlement_data = []
            calculated_total = 0
            
            for _, row in clean_df.iterrows():
                try:
                    name = str(row[name_col]).strip()
                    amount = pd.to_numeric(row[amount_col], errors='coerce')
                    
                    # 跳过无效数据
                    if pd.isna(amount) or name in ['nan', '', 'NaN']:
                        continue
                    
                    emp_id = None
                    if id_col and id_col in df.columns:
                        emp_id = str(row[id_col]) if pd.notna(row[id_col]) else None
                    
                    settlement_data.append({
                        'employee_id': emp_id,
                        'employee_name': name,
                        'settlement_amount': float(amount),
                        'source_type': 'account_bill'
                    })
                    
                    calculated_total += amount
                    
                except Exception as e:
                    print(f"处理行数据时出错: {str(e)}")
                    continue
            
            # 检查是否有汇总冲突
            summary_conflict = False
            if original_total is not None:
                try:
                    original_total = float(original_total)
                    # 允许小的浮点数误差
                    if abs(calculated_total - original_total) > 0.01:
                        summary_conflict = True
                except:
                    pass
            
            return settlement_data, calculated_total, summary_conflict
            
        except Exception as e:
            raise Exception(f"计算结算金额失败: {str(e)}")
    
    def __call__(self, state: WorkflowState) -> WorkflowState:
        """Agent主函数"""
        logger.agent_start("对账表处理 Agent")
        try:
            # 1. 找到对账单sheet
            logger.info("正在查找对账单Sheet...")
            sheet_name = self.find_account_bill_sheet(state)
            if not sheet_name:
                error_msg = "未在文件中找到被识别为“对账单”的Sheet。"
                logger.error(error_msg)
                state["errors"] = state.get("errors", []) + [error_msg]
                return state
            logger.success(f"找到对账单Sheet: '{sheet_name}'")
            
            file_path = state["file_path"]

            # 2. LLM语义映射：找出关键列的真实名称
            logger.info("调用Kimi模型分析对账单列结构...")
            column_mapping = self.analyze_column_mapping(file_path, sheet_name)
            llm_output_data = {
                "员工姓名列": column_mapping.employee_name_column,
                "结算金额列": column_mapping.settlement_amount_column,
                "员工ID列": column_mapping.employee_id_column or "未找到",
                "汇总行标识": column_mapping.summary_row_indicators or "无"
            }
            reasoning = "模型通过分析数据样本的语义，识别出最可能代表姓名、金额等核心信息的列。"
            logger.llm_result("Kimi列映射分析结果", llm_output_data, reasoning)

            # 3. 代码精确定位：根据列名找到表头行号
            logger.info("正在定位表头行...")
            df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
            header_row_index = self.find_header_row(df_raw, column_mapping)
            logger.success(f"成功定位到表头在第 {header_row_index + 1} 行。")
            
            # 4. 使用正确的表头，重新加载整个工作表
            logger.info("正在加载并清洗数据...")
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row_index)
            
            # 5. 计算结算金额
            settlement_data, total_amount, summary_conflict = self.calculate_settlement_amounts(df, column_mapping)
            
            if not settlement_data:
                raise Exception("未能从对账单中提取到任何有效的结算数据行。")
            
            logger.success(f"成功提取 {len(settlement_data)} 条结算记录，计算总金额为: {total_amount:,.2f} 元。")
            if summary_conflict:
                logger.error("计算出的总金额与表格中的合计行不符，请注意核对。")

            # 6. 保存到数据库
            logger.info("正在将结算数据保存到数据库...")
            self.db.save_settlement_amounts(state["project_id"], settlement_data)
            employees = [{'employee_id': item['employee_id'], 'employee_name': item['employee_name']} for item in settlement_data]
            self.db.save_employees(state["project_id"], employees)
            logger.success("数据保存成功。")
            
            # 7. 更新状态
            state["settlement_data"] = settlement_data
            state["current_step"] = "account_bill_processed"
            if not state.get("final_result"):
                state["final_result"] = {}
            state["final_result"]["summary_conflict"] = summary_conflict
            state["final_result"]["settlement_total"] = total_amount
            
            logger.agent_end("对账表处理 Agent")
            return state
            
        except Exception as e:
            error_msg = f"对账表处理失败: {str(e)}"
            logger.error(error_msg)
            state["errors"] = state.get("errors", []) + [error_msg]
            logger.agent_end("对账表处理 Agent")
            return state