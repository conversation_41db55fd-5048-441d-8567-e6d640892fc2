# -*- coding: utf-8 -*-
"""
@author: zhang
@software: PyCharm
@file: custom_chat_model.py
@time: 2025/7/16 23:03
"""
import os
from dotenv import load_dotenv
from langchain_community.chat_models.moonshot import MoonshotChat
from langchain_core.messages import HumanMessage

# 加载 .env 文件中的环境变量
load_dotenv()

# Kimi 模型名称，例如 moonshot-v1-8k, moonshot-v1-32k, moonshot-v1-128k
# 我们从 .env 的 MODEL_NAME 读取
model_name = os.getenv("MODEL_NAME", "moonshot-v1-8k") 
temperature = float(os.getenv("TEMPERATURE", 0.3)) # Kimi 的 temperature 范围是 0.0 到 1.0

# 检查必要的环境变量是否存在
if not os.environ["MOONSHOT_API_KEY"]:
    raise ValueError("请确保 .env 文件中设置了 OPENAI_API_KEY 作为 Kimi 的 API key")

# 初始化 MoonshotChat 模型
# MoonshotChat 会自动使用 MOONSHOT_API_KEY 环境变量
# 它不直接支持 base_url 参数，默认使用官方地址
chat_model = MoonshotChat(
    model=model_name,
    temperature=temperature,
)

def get_chat_model():
    """返回已初始化的 MoonshotChat 模型实例"""
    return chat_model

if __name__ == '__main__':
    # 示例用法
    print(f"正在使用模型: {model_name}")
    model = get_chat_model()
    messages = [
        HumanMessage(content="你好，介绍一下月之暗面。")
    ]
    
    print("正在调用模型...")
    try:
        response = model.invoke(messages)
        print("\n模型响应:")
        print(response.content)
    except Exception as e:
        print(f"\n调用模型时出错: {e}")
        print("请检查您的 API 密钥和网络连接。")
