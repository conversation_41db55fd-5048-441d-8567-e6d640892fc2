import pandas as pd
import os
import re
import os
from typing import Dict, List, Any, Tuple
from langchain_community.chat_models.moonshot import MoonshotChat
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.output_parsers import JsonOutputParser
from pydantic import BaseModel, Field

from ..state import WorkflowState
from ..database import DatabaseManager
from ..utils import get_excel_sample, logger

class SheetClassification(BaseModel):
    """Sheet分类结果"""
    sheet_name: str = Field(description="Sheet名称")
    classification: str = Field(description="分类结果: 对账单/用工统计表/发薪流水/应发薪表")
    confidence: float = Field(description="置信度0-1")
    key_columns: List[str] = Field(description="关键列名")

class FileAnalysisResult(BaseModel):
    """文件分析结果"""
    sheets: List[SheetClassification] = Field(description="所有sheet的分类结果")
    calculation_mode: str = Field(description="核额模式")
    project_name: str = Field(description="项目名称")

class FileParserAgent:
    """文件解析分类Agent"""
    
    def __init__(self):
        model_name = os.getenv("MODEL_NAME", "moonshot-v1-8k")
        temperature = float(os.getenv("TEMPERATURE", 0.3))
        
        self.llm = MoonshotChat(
            model=model_name,
            temperature=temperature,
        )
        self.db = DatabaseManager()
        self.parser = JsonOutputParser(pydantic_object=FileAnalysisResult)
        
    def get_sheets_summary(self, file_path: str) -> Dict[str, Any]:
        """获取所有sheet的摘要信息，用于LLM分析"""
        try:
            excel_file = pd.ExcelFile(file_path)
            sheets_summary = {}
            
            for sheet_name in excel_file.sheet_names:
                sample_str = get_excel_sample(file_path, sheet_name, sample_rows=10)
                if sample_str:
                    sheets_summary[sheet_name] = sample_str
            
            return sheets_summary
        except Exception as e:
            raise Exception(f"解析Excel文件获取摘要失败: {str(e)}")
    
    def classify_sheets(self, file_path: str, sheets_summary: Dict[str, str]) -> FileAnalysisResult:
        """使用LLM分类sheet类型"""
        
        # 构建分析提示
        summary_prompts = []
        for sheet_name, sample_data in sheets_summary.items():
            summary_prompts.append(f"""
Sheet Name: "{sheet_name}"
Data Sample (first 10 non-empty rows):
{sample_data}
""")
        
        system_prompt = f"""你是一个专业的保理核额审查数据分析师。请分析Excel文件中的sheets，并严格按照以下JSON格式返回结果：

```json
{{
  "sheets": [
    {{
      "sheet_name": "string",
      "classification": "对账单 | 用工统计表 | 发薪流水 | 应发薪表 | 汇总表 | 其他",
      "confidence": "float (0.0-1.0)",
      "key_columns": ["string"]
    }}
  ],
  "calculation_mode": "账单模式-先发后融 | 账单模式-先融后发 | 非账单模式-先发后融 | 非账单模式-先融后发",
  "project_name": "string"
}}
```

请根据以下规则进行分析：
- **sheet分类**:
  - `对账单`: 包含详细的、逐人逐项的结算金额、工时、单价等。
  - `用工统计表`: 包含详细的、逐人逐项的工时统计、出勤记录等。
  - `发薪流水`: 包含详细的、逐人逐项的实际发放金额记录。
  - `应发薪表`: 包含详细的、逐人逐项的应发金额记录。
  - `汇总表`: 如果一个sheet只包含高度概括的、非逐人逐项的合计数据（例如，只有几行，显示总应收、总实发等），则分类为“汇总表”。
  - `其他`: 不属于以上任何一种的，分类为“其他”。
- **核额模式判断**:
  - **仅** 使用 `对账单`、`用工统计表`、`发薪流水`、`应发薪表` 来判断模式。
  - **忽略** `汇总表` 和 `其他` 类型的sheet来判断计算模式。
  - `账单模式-先发后融`: 对账单 + 发薪流水
  - `账单模式-先融后发`: 对账单 + 应发薪表
  - `非账单模式-先发后融`: 用工统计表 + 发薪流水
  - `非账单模式-先融后发`: 用工统计表 + 应发薪表
- **项目名称提取**: 从sheet名称或数据中提取项目名称。
"""

        human_prompt = f"""请分析以下Excel文件的sheets信息，并按要求返回JSON：

{chr(10).join(summary_prompts)}
"""

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=human_prompt)
        ]
        
        # 移除旧的打印逻辑
        # from rich.panel import Panel
        # from rich.console import Console
        # console = Console()
        # console.print(Panel(system_prompt, title="[blue]System Prompt to Kimi[/blue]", border_style="blue"))
        # console.print(Panel(human_prompt, title="[green]Human Prompt to Kimi[/green]", border_style="green"))

        # 调用LLM
        response = self.llm.invoke(messages)
        response_content = response.content
        
        try:
            # 尝试从响应中提取JSON
            match = re.search(r"```json\n(.*)\n```", response_content, re.DOTALL)
            if match:
                json_str = match.group(1)
            else:
                json_str = response_content
            
            # 解析JSON并转换为Pydantic模型
            parsed_json = self.parser.parse(json_str)
            
            # 将可能不规范的JSON转换为我们期望的Pydantic模型
            sheets = []
            for sheet_data in parsed_json.get("sheets", parsed_json.get("sheet分析", parsed_json.get("sheet识别", []))):
                sheets.append(SheetClassification(
                    sheet_name=sheet_data.get("sheet_name", sheet_data.get("sheet名称")),
                    classification=sheet_data.get("classification", sheet_data.get("类型")),
                    confidence=sheet_data.get("confidence", 0.9),
                    key_columns=sheet_data.get("key_columns", sheet_data.get("判断依据", []))
                ))
            
            project_name_info = parsed_json.get("project_name", parsed_json.get("项目信息", {}))
            if isinstance(project_name_info, dict):
                project_name = project_name_info.get("项目名称", "未知项目")
            else:
                project_name = project_name_info

            result = FileAnalysisResult(
                sheets=sheets,
                calculation_mode=parsed_json.get("calculation_mode", parsed_json.get("核额模式")),
                project_name=project_name
            )
            
            return result
        except Exception as e:
            # 如果解析失败
            logger.error(f"LLM响应解析失败: {e}")
            raise Exception(f"FileParserAgent LLM解析失败: {e}")


    def __call__(self, state: WorkflowState) -> WorkflowState:
        """Agent主函数"""
        logger.agent_start("文件解析分类 Agent")
        try:
            # 1. 智能获取所有sheet的摘要信息
            logger.info("正在读取并解析Excel文件...")
            sheets_summary = self.get_sheets_summary(state["file_path"])
            logger.success(f"成功读取到 {len(sheets_summary)} 个sheet.")
            
            # 2. 使用LLM进行分类
            logger.info("调用Kimi模型进行文件结构分析...")
            analysis_result = self.classify_sheets(state["file_path"], sheets_summary)
            
            # 使用新的logger格式化输出LLM结果
            llm_output_data = {
                "项目名称": analysis_result.project_name,
                "核算模式": analysis_result.calculation_mode,
                "Sheet分类详情": [f"{s.sheet_name} -> {s.classification}" for s in analysis_result.sheets]
            }
            reasoning = "模型根据各Sheet的数据样本和预设规则，识别出核心业务表（如对账单、发薪表），并据此判断出整体的计算模式。"
            logger.llm_result("Kimi分析结果", llm_output_data, reasoning)

            # 3. 创建项目记录
            logger.info("正在创建项目记录到数据库...")
            project_id = self.db.create_project(
                project_name=analysis_result.project_name,
                file_path=state["file_path"]
            )
            logger.success(f"项目创建成功，ID: {project_id}")
            
            # 4. 更新状态
            state["project_id"] = project_id
            state["sheet_classifications"] = {
                sheet.sheet_name: sheet.classification
                for sheet in analysis_result.sheets
            }
            state["sheet_key_columns"] = {
                sheet.sheet_name: sheet.key_columns
                for sheet in analysis_result.sheets
            }
            state["calculation_mode"] = analysis_result.calculation_mode
            state["project_name"] = analysis_result.project_name
            state["current_step"] = "file_parsed"
            
            logger.agent_end("文件解析分类 Agent")
            return state
            
        except Exception as e:
            error_msg = f"文件解析失败: {str(e)}"
            logger.error(error_msg)
            state["errors"] = state.get("errors", []) + [error_msg]
            state["current_step"] = "error"
            logger.agent_end("文件解析分类 Agent")
            return state