import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import os
import os
import re
import json
from langchain_community.chat_models import MoonshotChat
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel, Field

from ..state import WorkflowState
from ..database import DatabaseManager
from ..utils import get_excel_sample, logger

class PayrollColumnMapping(BaseModel):
    """发薪流水列映射结果"""
    employee_id_column: Optional[str] = Field(description="员工ID列名")
    employee_name_column: str = Field(description="员工姓名列名")
    payroll_amount_column: str = Field(description="发薪金额列名")
    payroll_type: str = Field(description="发薪类型: actual_pay 或 expected_pay")

class PayrollProcessor:
    """发薪流水处理Agent"""
    
    def __init__(self):
        model_name = os.getenv("MODEL_NAME", "moonshot-v1-8k")
        temperature = float(os.getenv("TEMPERATURE", 0.3))
        self.llm = MoonshotChat(model=model_name, temperature=temperature)
        self.db = DatabaseManager()
    
    def find_payroll_sheet(self, state: WorkflowState) -> Optional[str]:
        """找到发薪流水或应发薪表sheet"""
        for sheet_name, classification in state["sheet_classifications"].items():
            if classification in ["发薪流水", "应发薪表"]:
                return sheet_name
        return None
    
    def analyze_column_mapping(self, file_path: str, sheet_name: str, classification: str) -> PayrollColumnMapping:
        """使用LLM分析原始数据，找出关键列的实际名称"""
        
        sample_data_str = get_excel_sample(file_path, sheet_name, sample_rows=20)
        if not sample_data_str:
            raise Exception(f"无法从sheet '{sheet_name}' 中读取到有效的数据样本。")
        payroll_type = "actual_pay" if classification == "发薪流水" else "expected_pay"

        system_prompt = f"""你是一个顶级的Excel数据分析师。你的任务是分析给定的表格原始数据样本，告诉我用于识别员工和发薪金额的关键列的 **实际名称** 是什么。

        **任务**:
        请仔细分析下面的原始数据，并严格按照这个JSON格式返回你的分析结果:
        ```json
        {{
          "employee_id_column": "代表员工ID的列的实际名称 (如果存在)",
          "employee_name_column": "代表员工姓名的列的实际名称",
          "payroll_amount_column": "代表发薪金额的列的实际名称",
          "payroll_type": "{payroll_type}"
        }}
        ```

        **关键列识别指南**:
        1. `employee_name_column`: 找到最能代表 **员工姓名** 的那一列的名称。
        2. `payroll_amount_column`: 找到最能代表 **发薪金额** 的列的名称。
           - 如果是 **发薪流水** (payroll_type: actual_pay), 请寻找代表 **实发金额** 的列, 如 "实发金额", "到账金额"。
           - 如果是 **应发薪表** (payroll_type: expected_pay), 请寻找代表 **应发金额** 的列, 如 "应发工资"。
        3. `employee_id_column`: 找到代表 **员工工号或编号** 的列的名称。如果找不到，可以返回 `null`。
        4. `payroll_type`: 此字段的值必须是 `{payroll_type}`。

        你返回的必须是数据中真实存在的列名。"""

        human_prompt = f"""这是 '{classification}' 表格的前20行原始数据:
        
        {sample_data_str}
        
        请返回包含上述关键列实际名称的JSON对象。"""

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=human_prompt)
        ]
        
        # 移除旧的打印逻辑

        # 调用LLM
        response = self.llm.invoke(messages)
        response_content = response.content

        try:
            # 尝试从响应中提取JSON
            match = re.search(r"```json\n(.*)\n```", response_content, re.DOTALL)
            if match:
                json_str = match.group(1)
            else:
                json_str = response_content
            
            # 显式地从字典创建Pydantic模型
            parsed_dict = json.loads(json_str)
            return PayrollColumnMapping(**parsed_dict)
        except (json.JSONDecodeError, TypeError, ValueError) as e:
            # 如果LLM返回的不是有效的JSON，或者Pydantic验证失败，则直接抛出异常
            raise Exception(f"LLM返回的列映射结果无效或格式错误: {e}\n原始返回内容:\n{response_content}")
    
    def find_header_row(self, df_raw: pd.DataFrame, column_mapping: PayrollColumnMapping) -> int:
        """
        在原始DataFrame中，通过代码定位包含所有关键列名的表头行。
        """
        required_cols = [
            column_mapping.employee_name_column,
            column_mapping.payroll_amount_column
        ]
        if column_mapping.employee_id_column:
            required_cols.append(column_mapping.employee_id_column)

        for i, row in df_raw.head(10).iterrows():
            row_values = set(str(v).strip() for v in row.values)
            if all(col in row_values for col in required_cols):
                return i
        
        raise Exception(f"在表格中未能定位到包含所有关键列 '{', '.join(required_cols)}' 的表头行。")
    
    def match_employees_with_settlement(self, payroll_data: List[Dict], settlement_data: List[Dict]) -> Tuple[List[Dict], int, int, List[Dict]]:
        """
        将发薪数据与结算数据进行人员匹配。
        返回: (匹配上的发薪数据, 匹配成功数, 未匹配数, 未匹配人员列表)
        """
        matched_payroll = []
        unmatched_payroll = []
        
        # 创建结算数据的查找集合，同时支持ID和姓名
        settlement_ids = {item['employee_id'] for item in settlement_data if item.get('employee_id')}
        settlement_names = {item['employee_name'] for item in settlement_data if item.get('employee_name')}
        
        for payroll_item in payroll_data:
            is_matched = False
            # 优先用ID匹配
            if payroll_item.get('employee_id') and payroll_item['employee_id'] in settlement_ids:
                is_matched = True
            # 其次用姓名匹配
            elif payroll_item.get('employee_name') and payroll_item['employee_name'] in settlement_names:
                is_matched = True
            
            if is_matched:
                matched_payroll.append(payroll_item)
            else:
                unmatched_payroll.append(payroll_item)
        
        return matched_payroll, len(matched_payroll), len(unmatched_payroll), unmatched_payroll
    
    def calculate_payroll_amounts(self, df: pd.DataFrame, column_mapping: PayrollColumnMapping) -> Tuple[List[Dict], float]:
        """计算发薪金额"""
        try:
            # 移除汇总行
            clean_df = self.remove_summary_rows(df, column_mapping)
            
            payroll_data = []
            total_amount = 0
            
            name_col = column_mapping.employee_name_column
            amount_col = column_mapping.payroll_amount_column
            id_col = column_mapping.employee_id_column
            
            for _, row in clean_df.iterrows():
                try:
                    name = str(row[name_col]).strip()
                    if name in ['nan', '', 'NaN']:
                        continue
                    
                    amount = pd.to_numeric(row[amount_col], errors='coerce')
                    if pd.isna(amount):
                        continue
                    
                    emp_id = None
                    if id_col and id_col in df.columns:
                        emp_id = str(row[id_col]) if pd.notna(row[id_col]) else None
                    
                    payroll_data.append({
                        'employee_id': emp_id,
                        'employee_name': name,
                        'payroll_amount': float(amount),
                        'source_type': column_mapping.payroll_type
                    })
                    
                    total_amount += amount
                    
                except Exception as e:
                    print(f"处理行数据时出错: {str(e)}")
                    continue
            
            return payroll_data, total_amount
            
        except Exception as e:
            raise Exception(f"计算发薪金额失败: {str(e)}")
    
    def remove_summary_rows(self, df: pd.DataFrame, column_mapping: PayrollColumnMapping) -> pd.DataFrame:
        """移除汇总行"""
        try:
            # 创建一个初始全为True的掩码，索引与df对齐
            mask = pd.Series(True, index=df.index)

            # 根据员工姓名列识别
            name_col = column_mapping.employee_name_column
            if name_col in df.columns:
                summary_indicators = ["合计", "总计", "汇总", "小计"]
                for indicator in summary_indicators:
                    # 使用 .loc 确保索引对齐
                    mask.loc[df[name_col].astype(str).str.contains(indicator, na=False)] = False

            # 根据员工ID列识别（如果存在）
            id_col = column_mapping.employee_id_column
            if id_col and id_col in df.columns:
                mask.loc[df[id_col].isna()] = False
            
            # 使用 .loc 应用掩码
            return df.loc[mask].copy()

        except Exception as e:
            print(f"移除汇总行时出错: {str(e)}")
            return df
    
    def __call__(self, state: WorkflowState) -> WorkflowState:
        """Agent主函数"""
        logger.agent_start("发薪数据处理 Agent")
        try:
            # 1. 找到发薪流水或应发薪表sheet
            logger.info("正在查找发薪数据Sheet...")
            sheet_name = self.find_payroll_sheet(state)
            if not sheet_name:
                logger.info("未找到发薪相关Sheet，跳过此步骤。")
                logger.agent_end("发薪数据处理 Agent")
                return state
            
            classification = state["sheet_classifications"][sheet_name]
            logger.success(f"找到发薪数据Sheet: '{sheet_name}' (类型: {classification})")
            
            file_path = state["file_path"]

            # 2. LLM语义映射
            logger.info(f"调用Kimi模型分析'{classification}'列结构...")
            column_mapping = self.analyze_column_mapping(file_path, sheet_name, classification)
            llm_output_data = {
                "员工姓名列": column_mapping.employee_name_column,
                "发薪金额列": column_mapping.payroll_amount_column,
                "员工ID列": column_mapping.employee_id_column or "未找到",
                "发薪类型": column_mapping.payroll_type
            }
            reasoning = "模型根据表名和数据样本，识别出代表姓名和金额的列，并确定是实发薪酬还是应发薪酬。"
            logger.llm_result("Kimi列映射分析结果", llm_output_data, reasoning)

            # 3. 代码精确定位表头并加载数据
            logger.info("正在定位表头并加载数据...")
            df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
            header_row_index = self.find_header_row(df_raw, column_mapping)
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row_index)
            logger.success(f"成功从第 {header_row_index + 1} 行加载数据。")

            # 4. 计算发薪金额
            logger.info("正在清洗数据并计算发薪总额...")
            payroll_data, total_payroll_amount_unmatched = self.calculate_payroll_amounts(df, column_mapping)
            if not payroll_data:
                raise Exception("未能从发薪数据中提取到任何有效的发薪数据行。")
            logger.success(f"成功提取 {len(payroll_data)} 条发薪记录，总金额: {total_payroll_amount_unmatched:,.2f} 元。")

            # 5. 与结算数据进行人员匹配
            logger.info("正在与对账单/用工表进行人员匹配...")
            settlement_data = state.get("settlement_data", [])
            if settlement_data:
                matched_payroll, matched_count, unmatched_count, unmatched_list = self.match_employees_with_settlement(payroll_data, settlement_data)
                
                # 准备表格数据
                summary_rows = [
                    ["对账单/用工表总人数", str(len(settlement_data))],
                    ["发薪表总人数", str(len(payroll_data))],
                    ["[green]匹配成功人数[/green]", f"[green]{matched_count}[/green]"],
                    ["[red]匹配失败人数[/red]", f"[red]{unmatched_count}[/red]"]
                ]
                logger.table("人员匹配统计", ["项目", "数量"], summary_rows)

                if unmatched_list:
                    unmatched_rows = [[item.get('employee_id', 'N/A'), item['employee_name'], f"{item['payroll_amount']:,.2f}"] for item in unmatched_list]
                    logger.table("[yellow]未匹配上的人员列表 (发薪表中存在，但对账单中不存在)[/yellow]", ["员工ID", "姓名", "发薪金额"], unmatched_rows)

                payroll_data = matched_payroll
                total_amount = sum(item['payroll_amount'] for item in payroll_data)
                
                if not state.get("final_result"):
                    state["final_result"] = {}
                state["final_result"]["matched_employees"] = matched_count
                state["final_result"]["unmatched_employees"] = unmatched_count
                state["final_result"]["total_settlement_employees"] = len(settlement_data)
                state["final_result"]["total_payroll_employees"] = len(payroll_data) + unmatched_count
            else:
                total_amount = total_payroll_amount_unmatched
            
            # 6. 保存到数据库和更新状态
            logger.info("正在将匹配后的发薪数据保存到数据库...")
            self.db.save_payroll_amounts(state["project_id"], payroll_data)
            logger.success("数据保存成功。")

            state["payroll_data"] = payroll_data
            state["current_step"] = "payroll_processed"
            if not state.get("final_result"):
                state["final_result"] = {}
            state["final_result"]["payroll_total"] = total_amount
            
            logger.agent_end("发薪数据处理 Agent")
            return state
            
        except Exception as e:
            error_msg = f"发薪流水处理失败: {str(e)}"
            logger.error(error_msg)
            state["errors"] = state.get("errors", []) + [error_msg]
            logger.agent_end("发薪数据处理 Agent")
            return state