#!/bin/bash
# 安装脚本

echo "🚀 开始安装保理核额审查系统..."

# 检查是否有uv
if ! command -v uv &> /dev/null; then
    echo "❌ uv 未安装，请先安装 uv: https://docs.astral.sh/uv/"
    exit 1
fi

echo "📦 安装Python依赖..."
uv sync

echo "🗄️ 初始化数据库..."
mkdir -p database

echo "📋 复制环境变量配置..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件，配置您的 OpenAI API Key"
fi

echo "✅ 安装完成！"
echo ""
echo "使用方法:"
echo "  uv run python main.py --help"
echo "  uv run python main.py demo"
echo "  uv run python main.py process testdata/测试V2.xlsx"
echo ""
echo "测试:"
echo "  uv run pytest tests/"