[project]
name = "hesuan-agent"
version = "0.1.0"
description = "保理核额审查多Agent系统"
authors = [{name = "Project Team", email = "<EMAIL>"}]
requires-python = ">=3.10"
dependencies = [
    "langgraph>=0.2.0",
    "langchain>=0.2.0",
    "langchain-openai>=0.1.0",
    "pandas>=2.0.0",
    "openpyxl>=3.1.0",
    "typer>=0.9.0",
    "rich>=13.0.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "langchain-community>=0.2.10",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.ruff]
line-length = 88
select = ["E", "F", "W", "I", "N", "UP", "B", "A", "C4", "T20"]
ignore = ["E501"]

[tool.hatch.build.targets.wheel]
packages = ["src/hesuan_agent"]

[tool.hatch.build.sources]
"src/hesuan_agent" = "src"